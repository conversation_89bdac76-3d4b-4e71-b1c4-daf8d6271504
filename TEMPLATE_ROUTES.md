# Template Routes Documentation

This document provides a comprehensive overview of all template-related API endpoints in the Cinepanda application.

## Base URL
All template routes are prefixed with `/api/templates` and require authentication.

## Authentication & Authorization
- **Authentication**: All routes require a valid JWT token (protected by `protect` middleware)
- **Authorization**: Routes use role-based access control with the following roles:
  - `admin`: Full access to all operations
  - `manager`: Can create, read, and update templates and items
  - `user`: Read-only access to items

---

## Equipment Templates

### Base Route: `/api/templates/equipments`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/equipments` | admin, manager | Get all equipment templates |
| POST | `/api/templates/equipments` | admin, manager | Create new equipment template |
| GET | `/api/templates/equipments/:id` | admin, manager | Get equipment template by ID |
| PUT | `/api/templates/equipments/:id` | admin, manager | Update equipment template |
| DELETE | `/api/templates/equipments/:id` | admin | Delete equipment template |

---

## Service Templates

### Base Route: `/api/templates/services`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/services` | admin, manager | Get all service templates |
| POST | `/api/templates/services` | admin, manager | Create new service template |
| GET | `/api/templates/services/:id` | admin, manager | Get service template by ID |
| PUT | `/api/templates/services/:id` | admin, manager | Update service template |
| DELETE | `/api/templates/services/:id` | admin | Delete service template |

---

## Accessory Templates

### Base Route: `/api/templates/accessories`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/accessories` | admin, manager | Get all accessory templates |
| POST | `/api/templates/accessories` | admin, manager | Create new accessory template |
| GET | `/api/templates/accessories/:id` | admin, manager | Get accessory template by ID |
| PUT | `/api/templates/accessories/:id` | admin, manager | Update accessory template |
| DELETE | `/api/templates/accessories/:id` | admin | Delete accessory template |

---

## Installation Templates

### Base Route: `/api/templates/installations`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/installations` | admin, manager | Get all installation templates |
| POST | `/api/templates/installations` | admin, manager | Create new installation template |
| GET | `/api/templates/installations/:id` | admin, manager | Get installation template by ID |
| PUT | `/api/templates/installations/:id` | admin, manager | Update installation template |
| DELETE | `/api/templates/installations/:id` | admin | Delete installation template |

---

## Equipment Items

### Base Route: `/api/templates/items/equipments`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/equipments` | admin, manager, user | Get all equipment items |
| POST | `/api/templates/items/equipments` | admin, manager | Create new equipment item |
| GET | `/api/templates/items/equipments/search` | admin, manager, user | Search equipment items |
| GET | `/api/templates/items/equipments/:id` | admin, manager, user | Get equipment item by ID |
| PUT | `/api/templates/items/equipments/:id` | admin, manager | Update equipment item |
| DELETE | `/api/templates/items/equipments/:id` | admin, manager | Delete equipment item |

---

## Service Items

### Base Route: `/api/templates/items/services`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/services` | admin, manager, user | Get all service items |
| POST | `/api/templates/items/services` | admin, manager | Create new service item |
| GET | `/api/templates/items/services/search` | admin, manager, user | Search service items |
| GET | `/api/templates/items/services/:id` | admin, manager, user | Get service item by ID |
| PUT | `/api/templates/items/services/:id` | admin, manager | Update service item |
| DELETE | `/api/templates/items/services/:id` | admin, manager | Delete service item |

---

## Accessory Items

### Base Route: `/api/templates/items/accessories`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/accessories` | admin, manager, user | Get all accessory items |
| POST | `/api/templates/items/accessories` | admin, manager | Create new accessory item |
| GET | `/api/templates/items/accessories/search` | admin, manager, user | Search accessory items |
| GET | `/api/templates/items/accessories/:id` | admin, manager, user | Get accessory item by ID |
| PUT | `/api/templates/items/accessories/:id` | admin, manager | Update accessory item |
| DELETE | `/api/templates/items/accessories/:id` | admin, manager | Delete accessory item |

---

## Installation Items

### Base Route: `/api/templates/items/installations`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/installations` | admin, manager, user | Get all installation items |
| POST | `/api/templates/items/installations` | admin, manager | Create new installation item |
| GET | `/api/templates/items/installations/search` | admin, manager, user | Search installation items |
| GET | `/api/templates/items/installations/:id` | admin, manager, user | Get installation item by ID |
| PUT | `/api/templates/items/installations/:id` | admin, manager | Update installation item |
| DELETE | `/api/templates/items/installations/:id` | admin, manager | Delete installation item |

---

## Notes

1. **Templates vs Items**: 
   - Templates are the main categories/types (equipment, services, accessories, installations)
   - Items are the specific instances within each template category

2. **Search Functionality**: 
   - All item categories support search functionality via `/search` endpoint
   - Search is available to all authenticated users (admin, manager, user)

3. **Role Permissions**:
   - **Admin**: Full CRUD access to all templates and items
   - **Manager**: Can create, read, and update templates and items (cannot delete templates)
   - **User**: Read-only access to items only

4. **Authentication**: All routes require a valid JWT token in the Authorization header

5. **Response Format**: All endpoints follow a consistent response format with status codes and structured data
